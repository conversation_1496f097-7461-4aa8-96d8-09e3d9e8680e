import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:onekitty/widgets/custom_international_phone_input.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/controllers/kitty_controller.dart';
import 'package:onekitty/helpers/extensions/text_styles.dart';
import 'package:onekitty/helpers/show_snack_bar.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:onekitty/models/chama/transfer_req.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/edit_kitty/tabs/mobile.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/edit_kitty/tabs/paybill.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/edit_kitty/tabs/till.dart';
import 'package:onekitty/screens/onboarding/passwd_req_screen.dart';
import 'package:onekitty/screens/widgets/payment_radio.dart';
import 'package:onekitty/utils/builTabs.dart';
import 'package:onekitty/utils/cache_keys.dart';
import 'package:onekitty/utils/utils_exports.dart';

import '../../../../widgets/text_form_field.dart';
import '../../contribution/edit_kitty/tabs/bank.dart';

class SignatoryInitiateTransactions extends StatefulWidget {
  const SignatoryInitiateTransactions({super.key});

  @override
  State<SignatoryInitiateTransactions> createState() =>
      _SignatoryInitiateTransactionsState();
}

class _SignatoryInitiateTransactionsState
    extends State<SignatoryInitiateTransactions> with TickerProviderStateMixin {
  TextEditingController amtCtr = TextEditingController();
  TextEditingController reasonCtr = TextEditingController();
  TextEditingController phoneCtr = TextEditingController();
  TextEditingController accountRefCtr = TextEditingController();
  TextEditingController accountCtr = TextEditingController();
  final ChamaDataController chamaDataController =
      Get.put(ChamaDataController());
  final ChamaController chamaController = Get.put(ChamaController());
  final KittyController kittyController = Get.put(KittyController());
  final formKey = GlobalKey<FormState>();
  String deviceToken = "";
  String? selectedChannel = "M-pesa";
  String deviceId = "";
  String deviceModel = "";
  late TabController tabController;
  bool showConfirm = false;
  String myPhone = "";
  PhoneNumber num = PhoneNumber(isoCode: 'KE');
  final box = GetStorage();
  String? transferType;

  final cardNumber = TextEditingController(),
      cardCVC = TextEditingController(),
      cardExpiry = TextEditingController();
  @override
  void initState() {
    super.initState();
    tabController = TabController(initialIndex: 0, length: 4, vsync: this);
    requestPermission();
    getToken();
    getDeviceInfo();
  }

  @override
  void dispose() {
    amtCtr.dispose();
    reasonCtr.dispose();
    phoneCtr.dispose();
    accountRefCtr.dispose();
    cardNumber.dispose();
    cardCVC.dispose();
    cardExpiry.dispose();
    accountCtr.dispose();
    super.dispose();
  }

  requestPermission() async {
    FirebaseMessaging messaging = FirebaseMessaging.instance;
    NotificationSettings settings = await messaging.requestPermission(
        alert: true, announcement: true, badge: true, sound: true);
    if (settings.authorizationStatus == AuthorizationStatus.authorized) {
    } else if (settings.authorizationStatus ==
        AuthorizationStatus.provisional) {
    } else {}
  }

  void getToken() async {
    await FirebaseMessaging.instance.getToken().then((value) {
      setState(() {
        deviceToken = value!;
      });
    });
  }

  void getDeviceInfo() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      setState(() {
        deviceId = androidInfo.id;
        deviceModel = androidInfo.model;
      });
    } else if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      print(
          'Running on ${iosInfo.utsname.machine} ${iosInfo.identifierForVendor} ${iosInfo.model}');
      setState(() {
        deviceId = iosInfo.identifierForVendor!;
        deviceModel = iosInfo.model;
      });
    } else {
      WebBrowserInfo webBrowserInfo = await deviceInfo.webBrowserInfo;
      setState(() {
        deviceId = webBrowserInfo.userAgent!;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    return Scaffold(
      //appBar: buildAppBar(context),
      body: SafeArea(
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 20),
          child: Form(
            key: formKey,
            child: SingleChildScrollView(
              child: Column(
                children: [
                  const RowAppBar(),
                  Text(
                    "Make a Transfer",
                    style: context.titleText,
                  ),
                  const SizedBox(
                    height: 5,
                  ),
                  const Text(
                    "Make transactions with ease",
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  Obx(() {
                    if (chamaController.penaltyKittyBalance > 0) {
                      return Column(
                        children: [
                          RadioListTile(
                            title: const Text('Transfer from Penalty'),
                            value: 'penalty',
                            groupValue: transferType,
                            onChanged: (value) {
                              setState(() {
                                transferType = value;
                              });
                            },
                          ),
                          RadioListTile(
                            title: const Text('Transfer from Chama'),
                            value: 'chama',
                            groupValue: transferType,
                            onChanged: (value) {
                              setState(() {
                                transferType = value;
                              });
                            },
                          ),
                        ],
                      );
                    } else {
                      return const SizedBox.shrink();
                    }
                  }),
                  CustomTextField(
                    controller: amtCtr,
                    isRequired: true,
                    showNoKeyboard: true,
                    labelText: "Enter amount",
                    validator: (p0) {
                      if (p0!.isEmpty) {
                        return "This field can't be empty";
                      }
                      return null;
                    },
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  DefaultTabController(
                      length: 4,
                      child: Column(
                        children: [
                          buildTabs(tabController, context),
                          SizedBox(
                            height: screenSize.height * 0.32.h,
                            child: Column(
                              children: [
                                Expanded(
                                    child: TabBarView(
                                        controller: tabController,
                                        children: [
                                      Mobile2(
                                        paymentChannelsBuilder2:
                                            PaymentChannelsBuilder2(
                                                selectedChannel:
                                                    selectedChannel ?? "",
                                                onChange: (String? value) {
                                                  setState(() {
                                                    selectedChannel = value;
                                                  });
                                                }),
                                        internationalPhoneNumberInput:
                                            CustomInternationalPhoneInput(
  onInputChanged: (num,
) {
                                            setState(() {
                                              myPhone = num.phoneNumber!;
                                            });
                                          },
                                          onInputValidated: (bool value) {
                                            print(value);
                                          },
                                           
                                          ignoreBlank: false,
                                           
                                          initialValue: num,
                                          textFieldController: phoneCtr,
                                          formatInput: true,
                                          keyboardType: const TextInputType
                                              .numberWithOptions(
                                              signed: true, decimal: true), 
                                          onSaved: (PhoneNumber number) {},
                                        ),
                                      ),
                                      PayBill2(
                                          paybillController: accountCtr,
                                          accountController: accountRefCtr),
                                      TillPage2(tillController: accountCtr),
                                      BankTab(accountNumber:  accountCtr),
                                      ]))
                              ],
                            ),
                          )
                        ],
                      )),
                  const SizedBox(
                    height: 10,
                  ),
                  CustomTextField(
                    controller: reasonCtr,
                    labelText: "Enter reason for transaction",
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  Obx(() => CustomKtButton(
                      isLoading: chamaController.isTransferReqLoading.isTrue,
                      onPress: () {
                        transferReq();
                      },
                      btnText: "Transfer")),
                  const SizedBox(
                    height: 15,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  transferReq() async {
    if (formKey.currentState!.validate()) {
      int? channelCode =
          kittyController.getNetworkCode(networkTitle: selectedChannel ?? "");
      String account = "";
      String accountRef = "";
      String paymentMode = '';
      debugPrint("${tabController.index}");
      if (tabController.index == 1) {
        channelCode = 63902;
        if (accountCtr.text.trim().isEmpty ||
            accountRefCtr.text.trim().isEmpty) {
          ToastUtils.showErrorToast(
              context, "Enter all required fields for Paybill", "Error");
          return;
        } else {
          account = accountCtr.text.trim();
          accountRef = accountRefCtr.text.trim();
          paymentMode = "PAYBILL";
        }
      } else if (tabController.index == 2) {
        channelCode = 63902;
        if (accountCtr.text.trim().isEmpty) {
          ToastUtils.showErrorToast(context, "Enter Till number", "Error");
          return;
        } else {
          account = accountCtr.text.trim();
          paymentMode = "TILL";
        }
             
      } else if(tabController.index == 0) {
        account = myPhone.substring(1);
        paymentMode = "WALLET";
        accountRef = '';
      }else  { 
        if (accountCtr.text.trim().isEmpty) {
          ToastUtils.showErrorToast(context, "Enter Bank account number", "Error");
          return;
        } else {
          channelCode = chamaDataController.channel;
          account = accountCtr.text.trim();
          paymentMode = "BANK";
        } 
      }

      TransferRequest request = TransferRequest(
        amount: int.parse(amtCtr.text.trim()),
        reason: reasonCtr.text.trim(),
        userId: chamaDataController.chama.value.member?.userId,
        chamaId: chamaDataController.chama.value.chama?.id,
        memberId: chamaDataController.chama.value.member?.id,
        channelCode: channelCode,
        recipientAccountNumber: CleanPhoneNumberInput.cleanPhoneNumber(account),
        recipientAccountRef: CleanPhoneNumberInput.cleanPhoneNumber(accountRef),
        transferMode: paymentMode,
        latitude: box.read(CacheKeys.lat),
        longitude: box.read(CacheKeys.long),
        deviceId: deviceId,
        deviceModel: deviceModel,
        isPenaltyKitty: transferType == "penalty" ? true : false,
      );
      bool res =
          await chamaController.transferReq(isConfirm: false, request: request);
      // if ((transferType == "penalty" &&
      //         int.parse(amtCtr.text.trim()) >
      //             chamaController.penaltyBal.toInt()) ||
      //     (transferType == "chama" &&
      //         int.parse(amtCtr.text.trim()) >
      //             chamaDataController.chama.value.chama?.totaBal)) {
      //   ToastUtils.showErrorToast(
      //       context, "Insufficient kitty balance", "Error");
      // } else
      if (res) {
        if (!mounted) return;
        Snack.show(res, chamaController.apiMessage.string);
        showDialog(
            context: context,
            builder: (context) {
          return AlertDialog(
  title: Text(
    chamaController.apiMessage.string,
    style: context.titleText?.copyWith(
      color: Theme.of(context).colorScheme.onSurface,
    ),
  ),
  content: Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    mainAxisSize: MainAxisSize.min,
    children: [
      // Transfer Amount - Primary Focus
      Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primaryContainer,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Text(
              "Transfer Amount",
              style: TextStyle(
                fontSize: 14,
                color: Theme.of(context).colorScheme.onPrimaryContainer.withOpacity(0.8),
              ),
            ),
            const SizedBox(height: 4),
            Text(
              "${FormattedCurrency().getFormattedCurrency(chamaController.amount)}",
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onPrimaryContainer,
              ),
            ),
          ],
        ),
      ),
      
      const SizedBox(height: 20),
      
      // Recipient Details
      _buildSection(
        context,
        title: "Recipient Details",
        children: [
          if( chamaController.accountName.value.isNotEmpty) ...[
          _buildDetailRow(context, "To", chamaController.accountName.value),
          ],
          if(chamaController.receiverAcc.value.isNotEmpty) ...[
          _buildDetailRow(context, "Account", chamaController.receiverAcc.value),
          ],
          if(selectedChannel != null && selectedChannel!.isNotEmpty) ...[
          _buildDetailRow(context, "Channel", selectedChannel!
          ),
           ],
            if (paymentMode == "WALLET")
            _buildDetailRow(context, "Reference", chamaController.receiverAccRef.value),
          if (paymentMode == "PAYBILL")
            _buildDetailRow(context, "Reference", chamaController.receiverAccRef.value),
        ],
      ),
      
      const SizedBox(height: 16),
      
      // Balance Summary
      _buildSection(
        context,
        title: "Balance Summary",
        children: [
          _buildDetailRow(
            context, 
            "Current Balance", 
            "${FormattedCurrency().getFormattedCurrency(chamaDataController.chama.value.chama?.totaBal)}",
          ),
          _buildDetailRow(
            context, 
            "New Balance", 
            "${FormattedCurrency().getFormattedCurrency(chamaController.newBalance.value)}",
          ),
        ],
      ),
      
      const SizedBox(height: 16),
      
      // Total Deduction - Important Info
      Container(
        width: double.infinity,
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.errorContainer,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              "Total Deduction",
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.onErrorContainer,
              ),
            ),
            Text(
              "${FormattedCurrency().getFormattedCurrency(chamaController.amount + chamaController.charges.value)}",
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
                color: Theme.of(context).colorScheme.onErrorContainer,
              ),
            ),
          ],
        ),
      ),
      
      const SizedBox(height: 12),
      
      // Expandable Charges Breakdown
      Theme(
        data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
        child: ExpansionTile(
          tilePadding: EdgeInsets.zero,
          childrenPadding: const EdgeInsets.only(left: 16, right: 16, bottom: 8),
          title: Text(
            "View Charges Breakdown",
            style: TextStyle(
              fontSize: 14,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          trailing: Icon(
            Icons.keyboard_arrow_down,
            size: 20,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
          ),
          children: [
            _buildChargeRow(
              context,
              "Platform Fees",
              "${FormattedCurrency().getFormattedCurrency( (chamaController.charges.value - chamaController.thirdPartyCharges.value).toPrecision(2))}",
            ),
            _buildChargeRow(
              context,
              "Processing Fees",
              "${FormattedCurrency().getFormattedCurrency( chamaController.thirdPartyCharges.value.toPrecision(2))}",
            ), 
            const SizedBox(height: 8),
            Container(
              height: 1,
              color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
            ),
            const SizedBox(height: 8),
            _buildChargeRow(
              context,
              "Total Charges",
              "${FormattedCurrency().getFormattedCurrency( chamaController.charges.value.toPrecision(2))} ",
              isTotal: true,
            ),
          ],
        ),
      ),
    ],
  ),
  actions: [
    SizedBox(
      height: 50,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          OutlinedButton(
            onPressed: () {
              Navigator.pop(context);
            },
            style: OutlinedButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.onSurface,
              side: BorderSide(
                color: Theme.of(context).colorScheme.outline,
              ),
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
            child: const Text("CANCEL"),
          ),
          Obx(() => CustomKtButton(
            isLoading: chamaController.isTransferReqLoading.isTrue,
            width: 120.w,
            onPress: () async {
              var isAuthenticated = await Get.to(
                () => AuthPasswdScreen(),
                arguments: [false],
              );
              if (isAuthenticated != null && isAuthenticated == true) {
                var resp = await chamaController.transferReq(
                  request: request,
                  isConfirm: true,
                );
                if (resp) {
                  if (!mounted) return;
                  Snack.show(resp, chamaController.apiMessage.string);
                  Navigator.pop(context);
                  Get.offAndToNamed(NavRoutes.viewingSingleChama);
                } else {
                  if (!mounted) return;
                  Snack.show(resp, chamaController.apiMessage.string);
                }
              } else {
                ToastUtils.showInfoToast(
                  context,
                  "You need to be authenticated to perform this operation.",
                  "Oops",
                );
              }
            },
            btnText: "CONFIRM",
          )),
        ],
      ),
    ),
  ],
);
  });
      } else {
        if (!mounted) return;
        Snack.show(res, chamaController.apiMessage.string);
      }
    }
  }
  
// Helper Methods
Widget _buildSection(BuildContext context, {required String title, required List<Widget> children}) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: Theme.of(context).colorScheme.onSurface,
        ),
      ),
      const SizedBox(height: 8),
      ...children,
    ],
  );
}

Widget _buildDetailRow(BuildContext context, String label, String value) {
  return Padding(
    padding: const EdgeInsets.only(bottom: 6),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontWeight: FontWeight.w500,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
      ],
    ),
  );
}

Widget _buildChargeRow(BuildContext context, String label, String value, {bool isTotal = false}) {
  return Padding(
    padding: const EdgeInsets.only(bottom: 4),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: isTotal ? 14 : 13,
            fontWeight: isTotal ? FontWeight.w600 : FontWeight.normal,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.8),
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: isTotal ? 14 : 13,
            fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
      ],
    ),
  );
}
}

//"PROCESSED/INITIALIZED/PROCESSING"
//"CONTRIBUTION , PENALTY"
