import 'package:date_time_format/date_time_format.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/models/user_kitties_model.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/main.dart' show isLight;
import 'package:onekitty/utils/asset_urls.dart';
import 'package:onekitty/utils/custom_image_view.dart';
import 'package:onekitty/utils/custom_outlined_button.dart';
import 'package:onekitty/utils/custom_text_style.dart';
import 'package:onekitty/utils/formatted_currency.dart';
import 'package:onekitty/utils/themes_colors.dart';

class ContributionKittyWidget extends StatelessWidget {
  final UserKitty kitty;

  const ContributionKittyWidget({
    super.key,
    required this.kitty,
  });

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: InkWell(
        onTap: () => _viewKittyDetails(context),
        borderRadius: BorderRadius.circular(20),
        child: Container(
          padding: EdgeInsets.all(12.w),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: isLight.value
                  ? [Colors.white, Colors.grey.shade50]
                  : [Colors.grey.shade900, Colors.grey.shade900],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(7),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(kitty.kittyType ?? "",
                  style: CustomTextStyles.titleSmallyellow),
              _buildHeader(),
              if (kitty.percentage != null) _buildProgressIndicator(),
              _buildCategoriesDisplay(),
              SizedBox(height: 16.h),
              _buildFooter(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Text(
            kitty.kitty?.title ?? "Unnamed Kitty",
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w800,
              color: isLight.value ? Colors.grey.shade800 : Colors.white,
              letterSpacing: -0.5,
            ),
          ),
        ),
        _buildBalanceChip(),
      ],
    );
  }

  Widget _buildBalanceChip() {
    return Text.rich(
        textAlign: TextAlign.end,
        TextSpan(children: [
          TextSpan(
              text: FormattedCurrency().getFormattedCurrency(
                  double.tryParse(kitty.kitty?.balance?.toString() ?? '') ?? 0),
              style: const TextStyle(
                  color: AppColors.primary,
                  fontSize: 16,
                  fontWeight: FontWeight.w600)),
        ]));
    // ignore: dead_code
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
      decoration: BoxDecoration(
        color: isLight.value
            ? Colors.green.shade50
            : Colors.green.shade900.withOpacity(0.3),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Text(
        FormattedCurrency().getFormattedCurrency(
            double.tryParse(kitty.kitty?.balance?.toString() ?? '') ?? 0),
        style: TextStyle(
          fontSize: 14.sp,
          fontWeight: FontWeight.w700,
          color: isLight.value ? Colors.green.shade700 : Colors.greenAccent,
        ),
      ),
    );
  }

  // ignore: unused_element
  Widget _buildProgressIndicator() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(10),
      child: LinearProgressIndicator(
        value: kitty.percentage ?? 0,
        minHeight: 3.h,
        backgroundColor: Colors.grey.shade300,
        valueColor:
            AlwaysStoppedAnimation(_getStatusColor(kitty.kittyStatus ?? "")),
      ),
    );
  }

  Widget _buildFooter() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: _buildInfoColumn(
            icon: Icons.calendar_today_rounded,
            title: 'Created',
            value: DateFormat('MMM dd, yyyy')
                .format(kitty.kitty?.createdAt?.toLocal() ?? DateTime.now()),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: _buildInfoColumn(
            icon: Icons.access_time_rounded,
            title: DateTimeFormat.relative(
              kitty.kitty?.endDate ?? DateTime.now(),
              levelOfPrecision: 1,
              prependIfBefore: '',
              ifNow: "Now",
              appendIfAfter: ' ago',
            ).contains('ago')
                ? 'Ended'
                : 'Ends In',
            value: DateTimeFormat.relative(
              kitty.kitty?.endDate ?? DateTime.now(),
              levelOfPrecision: 1,
              prependIfBefore: '',
              ifNow: "Now",
              appendIfAfter: ' ago',
            ),
          ),
        ),
        const SizedBox(width: 8),
        _buildStatusChip(),
      ],
    );
  }

  Widget _buildInfoColumn(
      {required IconData icon, required String title, required String value}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 18.w, color: Colors.grey.shade600),
            SizedBox(width: 4.w),
            Text(
              title,
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.w500,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
        SizedBox(height: 4.h),
        Text(
          value,
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w700,
            color: isLight.value ? Colors.grey.shade800 : Colors.white,
          ),
        ),
      ],
    );
  }

  Widget _buildStatusChip() {
    final statusColor = _getStatusColor(kitty.kittyStatus ?? "");
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
      decoration: BoxDecoration(
          color: statusColor.withOpacity(0.15),
          borderRadius: BorderRadius.circular(30)),
      child: Row(
        children: [
          Container(
            width: 8.w,
            height: 8.w,
            decoration: BoxDecoration(
              color: statusColor,
              shape: BoxShape.circle,
            ),
          ),
          SizedBox(width: 8.w),
          Text(
            kitty.kittyStatus ?? "Unknown",
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.w700,
              color: statusColor,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case "active":
        return const Color(0xFF00C853);
      case "completed":
        return const Color(0xFF6200EA);
      case "settlement initiated":
        return const Color(0xFFFFD600);
      default:
        return const Color(0xFFE53935);
    }
  }

  Widget _buildCategoriesDisplay() {
    // Check if categories exist and are not empty
    if (kitty.kitty?.categories == null || kitty.kitty!.categories!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: EdgeInsets.only(top: 8.h),
      child: SizedBox(
        height: 28.h,
        child: ListView.separated(
          scrollDirection: Axis.horizontal,
          itemCount: kitty.kitty!.categories!.length,
          separatorBuilder: (context, index) => SizedBox(width: 8.w),
          itemBuilder: (context, index) {
            final category = kitty.kitty!.categories![index];
            return _buildCategoryChip(category.name ?? '');
          },
        ),
      ),
    );
  }

  Widget _buildCategoryChip(String categoryName) {
    if (categoryName.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: const Color(0xFF2196F3).withOpacity(0.15),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF2196F3).withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 6.w,
            height: 6.w,
            decoration: const BoxDecoration(
              color: Color(0xFF2196F3),
              shape: BoxShape.circle,
            ),
          ),
          SizedBox(width: 6.w),
          Text(
            categoryName,
            style: TextStyle(
              fontSize: 11.sp,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF2196F3),
            ),
          ),
        ],
      ),
    );
  }

  // ignore: unused_element
  double _calculateProgress() {
    final start = kitty.kitty?.createdAt ?? DateTime.now();
    final end =
        kitty.kitty?.endDate ?? DateTime.now().add(const Duration(days: 1));
    final total = end.difference(start).inSeconds;
    final passed = DateTime.now().difference(start).inSeconds;
    return (passed / total).clamp(0.0, 1.0);
  }

  void _viewKittyDetails(BuildContext context) {
    final dataController = Get.put(DataController());
    dataController.kitty.value = kitty;
    Get.toNamed(NavRoutes.viewingsinglekittyScreen);
  }
}

class ContributionKittyWidgetE extends StatelessWidget {
  final UserKitty kitty;

  const ContributionKittyWidgetE({
    super.key,
    required this.kitty,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => _viewKittyDetails(context),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 7.w, vertical: 2.h),
        decoration: AppDecoration.shadow1Themed.copyWith(
          borderRadius: BorderRadiusStyle.roundedBorder6,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildTitleAndBalance(),
            _buildStatusAndLabel(),
            SizedBox(height: 10.h),
            _buildDateAndTimeInfo(),
            SizedBox(height: 3.h),
          ],
        ),
      ),
    );
  }

  Widget _buildTitleAndBalance() {
    return Padding(
      padding: EdgeInsets.only(right: 4.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: 7.h),
              child: Text(
                kitty.kitty?.title ?? "",
                overflow: TextOverflow.ellipsis,
                style: CustomTextStyles.labelMediumff545963,
              ),
            ),
          ),
          Text(
            FormattedCurrency().getFormattedCurrency(kitty.kitty?.balance),
            style: TextStyle(
              fontSize: 20.h,
              fontWeight: FontWeight.w900,
              color: isLight.value ? appTheme.gray900 : appTheme.whiteA700,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusAndLabel() {
    return Padding(
      padding: EdgeInsets.only(right: 4.w),
      child: Row(
        children: [
          _buildChip(kitty.kittyType ?? "", CustomTextStyles.titleSmallyellow),
          SizedBox(width: 10.w),
          _buildChip(
            kitty.kittyStatus ?? "",
            TextStyle(
              color: getKittyStatusColor(kitty.kittyStatus ?? ""),
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          Padding(
            padding: EdgeInsets.only(bottom: 3.h),
            child: Text(
              "Balance",
              style: CustomTextStyles.bodySmallGray900,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChip(String label, TextStyle style) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: AppDecoration.outlineIndigo.copyWith(
        borderRadius: BorderRadiusStyle.circleBorder16,
      ),
      child: Text(label, style: style),
    );
  }

  Widget _buildDateAndTimeInfo() {
    return Padding(
      padding: EdgeInsets.only(right: 4.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _buildDateInfo(),
          _buildViewButton(),
        ],
      ),
    );
  }

  Widget _buildDateInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildInfoRow(
          iconPath: AssetUrl.imgCalendar,
          label: "Created: ",
          value: DateFormat('MMM dd, yyyy').format(
            kitty.kitty?.createdAt?.toLocal() ?? DateTime.now(),
          ),
        ),
        SizedBox(height: 4.h),
        _buildInfoRow(
          iconPath: AssetUrl.imgClock,
          label: DateTimeFormat.relative(
            kitty.kitty?.endDate ?? DateTime.now(),
            levelOfPrecision: 1,
            prependIfBefore: 'Ends In ',
            ifNow: "Now",
            appendIfAfter: ' ago',
          ),
          value: "",
          isRelative: true,
        ),
      ],
    );
  }

  Widget _buildInfoRow({
    required String iconPath,
    required String label,
    required String value,
    bool isRelative = false,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomImageView(
          imagePath: iconPath,
          height: 24.h,
          width: 24.w,
        ),
        Padding(
          padding: EdgeInsets.only(
              left: 4.w,
              top: isRelative ? 4.h : 2.h,
              bottom: isRelative ? 0 : 3.h),
          child: RichText(
            text: TextSpan(
              children: [
                if (!isRelative)
                  TextSpan(
                    text: label,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontStyle: FontStyle.italic,
                      color: isLight.value
                          ? appTheme.gray90001
                          : appTheme.whiteA70001,
                    ),
                  )
                else
                  TextSpan(
                    text: label,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: isLight.value
                          ? appTheme.gray90001
                          : appTheme.whiteA70001,
                    ),
                  ),
                if (!isRelative)
                  TextSpan(
                    text: value,
                    style: CustomTextStyles.bodySmallGray900,
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildViewButton() {
    return CustomOutlinedButton(
      onPressed: () => _viewKittyDetails(Get.context!),
      width: 68.w,
      height: 20.h,
      text: "View",
      margin: EdgeInsets.all(1.w),
    );
  }

  void _viewKittyDetails(BuildContext context) {
    final dataController = Get.put(DataController());
    dataController.kitty.value = kitty;
    Get.toNamed(NavRoutes.viewingsinglekittyScreen);
  }
}

Color getKittyStatusColor(String status) {
  switch (status.toLowerCase()) {
    case "active":
    case "completed":
      return const Color(0xFF56AF57);
    case "settlement initiated":
      return const Color.fromARGB(255, 206, 104, 192);
    default:
      return const Color(0xFFEE5B60);
  }
}
