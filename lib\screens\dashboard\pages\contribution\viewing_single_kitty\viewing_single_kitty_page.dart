import 'dart:async';
import 'dart:io';
import 'dart:ui';
import 'package:fast_cached_network_image/fast_cached_network_image.dart';
import 'package:date_time_format/date_time_format.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_quill/flutter_quill.dart' as q;
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:onekitty/controllers/beneficiary_controller.dart';
import 'package:onekitty/controllers/events/controllers.dart';
import 'package:onekitty/controllers/kitty_controller.dart';
import 'package:onekitty/controllers/kitty_settings.dart';
import 'package:onekitty/helpers/connectivity_wrapper.dart';
import 'package:onekitty/helpers/extensions/text_styles.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/edit_kitty/whatsapp_link.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/viewing_single_kitty/widgets/whatsApp_groups_widget.dart';
import 'package:onekitty/utils/date_formatter.dart';
import 'package:onekitty/utils/my_quill_editor.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty/models/auth/user_model.dart';
import 'package:onekitty/controllers/contribute_controller.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/viewing_single_kitty/widgets/services_widget.dart';
import 'package:onekitty/utils/size_config.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart' as ptr;
import 'package:qr_flutter/qr_flutter.dart';
import 'package:share_plus/share_plus.dart';
import '../../../../../utils/utils_exports.dart';
import 'widgets/kitty_transactions_widget.dart';
import 'package:flutter/material.dart';
import 'widgets/beneficiary_card.dart' show BeneficiaryCard;
import 'package:onekitty/main.dart' show isLight;

class ViewingSingleKittyScreen extends StatefulWidget {
  const ViewingSingleKittyScreen({
    super.key,
  });

  static onRefresh() async {
    final UserKittyController controller = Get.find();
    final ContributeController getKittyController =
        Get.put(ContributeController());
    final DataController dataController = Get.put(DataController());

    try {
      controller.getLocalUser();
      getKittyController.kittyMedia.clear();
      Get.find<ContributeController>()
          .getWhatsapp(id: dataController.kitty.value.kitty?.iD ?? 0);
      getKittyController.getKitty(id: dataController.kitty.value.kitty?.iD);
      await Get.put(SettingsController()).fetchSettings(
        dataController.kitty.value.kitty?.iD ?? 0,
      );
    } catch (e) {
      if (kDebugMode) {
        print("Static refresh error: $e");
      }
    }
  }

  @override
  State<ViewingSingleKittyScreen> createState() =>
      _ViewingSingleKittyScreenState();
}

class _ViewingSingleKittyScreenState extends State<ViewingSingleKittyScreen>
    with TickerProviderStateMixin {
  final DataController dataController = Get.put(DataController());
  late TabController tabController;
  GlobalKey globalKey = GlobalKey();
  UserModelLatest user = UserModelLatest();
  q.QuillController quillController = q.QuillController.basic();
  final activeIndex = 0.obs;
  final UserKittyController controller = Get.find();
  final extendedScreen = 400.0.h.obs;
  int? kittyId;

  final KittyController kittyController = Get.put(KittyController());
  final ContributeController getKittyController =
      Get.put(ContributeController());
  final KittyDataController kittyData = Get.put(KittyDataController());
  final String greeting = getGreeting();
  DateTime tagetDate = DateTime.now().add(const Duration(days: 1));
  final carouselController = CarouselController();

  final ptr.RefreshController _refreshController =
      ptr.RefreshController(initialRefresh: true);

  @override
  void dispose() {
    super.dispose();
    // Use a post-frame callback to safely clear the list after the widget tree is unlocked
    WidgetsBinding.instance.addPostFrameCallback((_) {
      getKittyController.kittyMedia.clear();
    });
  }

  Future<void> onRefresh() async {
    // Execute refresh operations immediately without post-frame callback
    try {
      controller.getLocalUser();

      getKittyController.getKitty(id: dataController.kitty.value.kitty?.iD);
      Get.put(SettingsController()).fetchSettings(
        dataController.kitty.value.kitty?.iD ?? 0,
      );
      Get.put(ContributeController())
          .getWhatsapp(id: dataController.kitty.value.kitty?.iD ?? 0);
      Get.put(BeneficiaryController())
          .getBeneficiaries(dataController.kitty.value.kitty?.iD ?? 0);
    } catch (e) {
      if (kDebugMode) {
        print("Refresh error: $e");
      }
    } finally {
      // Always complete refresh to ensure the refresh indicator goes away
      // ignore: control_flow_in_finally
      if (!mounted) return; // Check if widget is still in the tree
      _refreshController.refreshCompleted();
    }
    // Return a future that completes when the refresh is done
    return Future.value();
  }

  Future<void> _onRefresh() async {
    await onRefresh();
    _refreshController.refreshCompleted();
  }

  void onLoading() async {
    // Handle loading state if you implement infinite scrolling
    await Future.delayed(const Duration(milliseconds: 500));
    _refreshController.loadComplete();
  }

  @override
  void initState() {
    super.initState();

    // getKittyController.kittyMedia.clear();
    _onRefresh();
    tabController = TabController(length: 2, vsync: this);
    tabController.addListener(() {
      extendedScreen.value = tabController.index == 0
          ? SizeConfig.screenHeight * 0.6
          : SizeConfig.screenHeight * 0.8;
    });
  }

  Future<void> _captureAndSharePng() async {
    try {
      RenderRepaintBoundary boundary =
          globalKey.currentContext?.findRenderObject() as RenderRepaintBoundary;

      var image = await boundary.toImage(pixelRatio: 5.0);

      ByteData byteData =
          await image.toByteData(format: ImageByteFormat.png) as ByteData;
      Uint8List pngBytes = byteData.buffer.asUint8List();

      final tempDir = await getTemporaryDirectory();
      final file = await File('${tempDir.path}/image.png').create();
      await file.writeAsBytes(pngBytes);

      await Share.shareXFiles(
        [XFile(file.path)],
      );
    } catch (e) {
      if (kDebugMode) {
        print(e.toString());
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: ConnectivityCheck(
        child: Scaffold(
          body: RefreshIndicator(
            onRefresh: () async {
              onRefresh();
            },
            color: Theme.of(context).primaryColor,
            backgroundColor: Theme.of(context).cardColor,
            strokeWidth: 3,
            displacement: 100,
            child: Obx(() {
              final hasMedia = getKittyController.kittyMedia.isNotEmpty;

              return CustomScrollView(
                physics:
                    const AlwaysScrollableScrollPhysics(), // This is critical for RefreshIndicator to work
                slivers: [
                  SliverAppBar(
                    leadingWidth: 80.w,
                    leading: InkWell(
                      onTap: () => Navigator.pop(context),
                      child: Container(
                          decoration: BoxDecoration(
                            color: Colors.black45,
                            borderRadius: BorderRadius.circular(25),
                          ),
                          margin: const EdgeInsets.all(6),
                          padding: const EdgeInsets.all(4),
                          alignment: Alignment.center,
                          child: const Row(children: [
                            Icon(Icons.navigate_before, color: Colors.white),
                            Text('Back', style: TextStyle(color: Colors.white))
                          ])),
                    ),
                    expandedHeight: hasMedia ? 250 : 50,
                    backgroundColor: Theme.of(context).scaffoldBackgroundColor,
                    pinned: true,
                    stretch: true,
                    flexibleSpace: hasMedia
                        ? const FlexibleSpaceBar(
                            collapseMode: CollapseMode.parallax,
                            background: KittyFlexibleSpacebar(),
                          )
                        : null,
                    actions:
                        // hasMedia
                        //     ? []
                        //     :

                        [
                      SizedBox(width: 8.w),
                      InkWell(
                        onTap: () async {
                          if (kittyController.isUploadingImage.value) {
                            return;
                          }
                          kittyController.pickImage(
                              context: context,
                              kittyId:
                                  dataController.kitty.value.kitty?.iD ?? 0,
                              name: dataController.kitty.value.kitty?.title ??
                                  '');
                        },
                        child: Obx(
                          () => kittyController.isUploadingImage.value
                              ? const CircleAvatar(
                                  backgroundColor: Colors.black38,
                                  child: CircularProgressIndicator(
                                      backgroundColor: Colors.white))
                              : const CircleAvatar(
                                  backgroundColor: Colors.black38,
                                  child: Icon(Icons.add_a_photo_outlined,
                                      color: Colors.white)),
                        ),
                      ),
                      SizedBox(width: 8.w),
                      InkWell(
                        onTap: () async {
                          String shareMsg =
                              "Kitty Title: ${dataController.kitty.value.kitty?.title ?? ""}\nClick: https://onekitty.co.ke/kitty/${dataController.kitty.value.kitty?.iD ?? 0}\n to Pay";
                          await Share.share(shareMsg, subject: 'Kitty Details');
                        },
                        child: const CircleAvatar(
                            backgroundColor: Colors.black38,
                            child: Icon(Icons.share, color: Colors.white)),
                      ),
                      SizedBox(width: 8.w),
                      InkWell(
                        onTap: () {
                          showModalBottomSheet(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10.0),
                            ),
                            context: context,
                            isScrollControlled: true,
                            constraints: BoxConstraints(
                              maxHeight: SizeConfig.screenHeight * .8,
                              maxWidth: SizeConfig.screenWidth,
                            ),
                            builder: (_) => SizedBox(
                              height: SizeConfig.screenHeight * .7,
                              width: SizeConfig.screenWidth,
                              child: Column(
                                children: [
                                  const SizedBox(
                                    height: 10,
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.all(15.0),
                                    child: RepaintBoundary(
                                      key: globalKey,
                                      child: Container(
                                        margin: const EdgeInsets.all(20.0),
                                        color: Colors.white,
                                        child: Column(
                                          children: [
                                            const Text(
                                              "Scan to Pay",
                                              style: TextStyle(
                                                fontWeight: FontWeight.bold,
                                                fontSize: 20,
                                              ),
                                            ),
                                            QrImageView(
                                              padding:
                                                  const EdgeInsets.all(10.0),
                                              data:
                                                  "onekitty.co.ke/kitty/${dataController.kitty.value.kitty?.iD}",
                                              version: QrVersions.auto,
                                              gapless: false,
                                              errorCorrectionLevel:
                                                  QrErrorCorrectLevel.H,
                                              embeddedImage: const AssetImage(
                                                  AssetUrl.logo4),
                                              embeddedImageStyle:
                                                  const QrEmbeddedImageStyle(
                                                size: Size(80, 80),
                                              ),
                                              size: 250.0,
                                            ),
                                            const SizedBox(
                                              height: 5.0,
                                            ),
                                            Text(
                                              "  onekitty.co.ke/kitty/${dataController.kitty.value.kitty?.iD} ",
                                              style: const TextStyle(
                                                color: Colors.blue,
                                                fontSize: 16,
                                              ),
                                            ),
                                            const SizedBox(height: 10),
                                            Text(
                                              dataController.kitty.value.kitty
                                                      ?.title ??
                                                  "\n",
                                              style:
                                                  const TextStyle(fontSize: 17),
                                            )
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                  Container(
                                    margin: const EdgeInsets.only(right: 10.0),
                                    child: ElevatedButton(
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: Theme.of(context)
                                              .colorScheme
                                              .primary,
                                          fixedSize: const Size(250, 20),
                                        ),
                                        child: Text(
                                          "share".tr,
                                          style: const TextStyle(
                                            color: Colors.white,
                                            fontSize: 15,
                                          ),
                                        ),
                                        onPressed: () async {
                                          _captureAndSharePng();
                                        }),
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                        child: const CircleAvatar(
                            backgroundColor: Colors.black38,
                            child: Icon(Icons.qr_code_2, color: Colors.white)),
                      ),
                      const SizedBox(width: 8),
                      if (kDebugMode)
                        InkWell(
                          onTap: () {
                            final kittyText =
                                "userkitty: ${dataController.kitty.value.toJson()} \n ################### \n kitty: ${dataController.kitty.value.kitty?.toJson()} ";
                            Clipboard.setData(ClipboardData(text: kittyText));
                            Get.snackbar(
                                'Copied!', 'Kitty details copied to clipboard',
                                snackPosition: SnackPosition.bottom,
                                backgroundColor: Colors.green,
                                colorText: Colors.white,
                                duration: const Duration(seconds: 2));
                          },
                          child: const CircleAvatar(
                              backgroundColor: Colors.black38,
                              child: Icon(Icons.copy, color: Colors.white)),
                        ),
                    ],
                  ),
                  SliverToBoxAdapter(
                    child: Padding(
                      padding:
                          EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Expanded(
                                child: Text(
                                  dataController.kitty.value.kitty?.title ?? "",
                                  style: TextStyle(
                                      color: isLight.value
                                          ? Colors.black
                                          : appTheme.whiteA700,
                                      fontWeight: FontWeight.w700,
                                      fontSize: 22.spMin),
                                  overflow: TextOverflow.visible,
                                ),
                              ),
                              Text(
                                  FormattedCurrency().getFormattedCurrency(
                                      dataController
                                          .kitty.value.kitty?.balance),
                                  style: CustomTextStyles.titleMediumSemiBold),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: EdgeInsets.symmetric(
                          horizontal: 16.w, vertical: 12.h),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          // Calendar icon container
                          Container(
                            padding: EdgeInsets.all(8.w),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              color: primaryColor.withOpacity(0.15),
                            ),
                            child: Image.asset(
                              'assets/images/icons/calendar.png',
                              height: 30.h,
                              width: 30.w,
                              color: primaryColor,
                            ),
                          ),
                          SizedBox(width: 12.w),
                          // Date information
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "Created: ${formatDate("${dataController.kitty.value.kitty?.createdAt}")}",
                                  style: TextStyle(
                                    fontSize: 14.spMin,
                                    fontWeight: FontWeight.w500,
                                    color: isLight.value
                                        ? appTheme.gray90001
                                        : appTheme.whiteA70001,
                                  ),
                                ),
                                SizedBox(height: 4.h),
                                Text(
                                  DateTimeFormat.relative(
                                    dataController.kitty.value.kitty?.endDate ??
                                        DateTime.now(),
                                    levelOfPrecision: 1,
                                    prependIfBefore: 'Ends In',
                                    ifNow: "Now",
                                    appendIfAfter: 'ago',
                                  ),
                                  style: TextStyle(
                                    color: isLight.value
                                        ? appTheme.gray90001
                                        : appTheme.whiteA70001,
                                    fontStyle: FontStyle.italic,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          // Status chip
                          _buildStatusChip(),
                        ],
                      ),
                    ),
                  ),

                  // Categories display
                  SliverToBoxAdapter(
                    child: _buildCategoriesDisplay(),
                  ),

                  SliverToBoxAdapter(
                      child: Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: 12.0.w, vertical: 4.h),
                    child: Column(
                      children: [
                        Align(
                          alignment: Alignment.topLeft,
                          child: Text(
                            'About: ',
                            style: TextStyle(
                                fontSize: 14.spMin,
                                color: isLight.value
                                    ? appTheme.gray900
                                    : appTheme.whiteA700,
                                fontWeight: FontWeight.w600),
                          ),
                        ),
                        QuillEditorWidget(
                            readMore: true,
                            text:
                                dataController.kitty.value.kitty?.description ??
                                    ""),
                      ],
                    ),
                  )),
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20.0),
                      child: Column(
                        children: [
                          const BeneficiaryCard(),
                          SizedBox(height: 8.h),
                          _buildRow(context),
                          SizedBox(height: 6.h),
                          _buildFrame1(context),
                          SizedBox(height: 8.h),
                          _buildTabs(context),
                        ],
                      ),
                    ),
                  ),
                  // const SliverToBoxAdapter(child: SizedBox(height: 50)),
                ],
              );
            }),
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip() {
    final statusColor =
        _getStatusColor(dataController.kitty.value.kittyStatus ?? "");
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
      decoration: BoxDecoration(
          color: statusColor.withOpacity(0.15),
          borderRadius: BorderRadius.circular(30)),
      child: Row(
        children: [
          Container(
            width: 8.w,
            height: 8.w,
            decoration: BoxDecoration(
              color: statusColor,
              shape: BoxShape.circle,
            ),
          ),
          SizedBox(width: 8.w),
          Text(
            dataController.kitty.value.kittyStatus ?? "Unknown",
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.w700,
              color: statusColor,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case "active":
        return const Color(0xFF00C853);
      case "completed":
        return const Color(0xFF6200EA);
      case "settlement initiated":
        return const Color(0xFFFFD600);
      default:
        return const Color(0xFFE53935);
    }
  }

  Widget _buildCategoriesDisplay() {
    // Check if categories exist and are not empty
    if (dataController.kitty.value.kitty?.categories == null ||
        dataController.kitty.value.kitty!.categories!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 12.0.w, vertical: 8.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Categories:',
            style: TextStyle(
              fontSize: 14.spMin,
              color: isLight.value ? appTheme.gray900 : appTheme.whiteA700,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 8.h),
          SizedBox(
            height: 32.h,
            child: ListView.separated(
              scrollDirection: Axis.horizontal,
              itemCount: dataController.kitty.value.kitty!.categories!.length,
              separatorBuilder: (context, index) => SizedBox(width: 8.w),
              itemBuilder: (context, index) {
                final category = dataController.kitty.value.kitty!.categories![index];
                return _buildCategoryChip(category.name ?? '');
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryChip(String categoryName) {
    if (categoryName.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
      decoration: BoxDecoration(
        color: const Color(0xFF2196F3).withOpacity(0.15),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF2196F3).withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 8.w,
            height: 8.w,
            decoration: const BoxDecoration(
              color: Color(0xFF2196F3),
              shape: BoxShape.circle,
            ),
          ),
          SizedBox(width: 8.w),
          Text(
            categoryName,
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF2196F3),
            ),
          ),
        ],
      ),
    );
  }

  /// Section Widget
  Widget _buildRow(BuildContext context) {
    final contributeController = Get.find<ContributeController>();
    return Obx(() =>
        Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
          Align(
            alignment: Alignment.topLeft,
            child: Text(
              'Connected WhatsApp groups: ',
              style: TextStyle(
                  fontSize: 14.spMin,
                  color: isLight.value ? Colors.black : Colors.white,
                  fontWeight: FontWeight.w600),
            ),
          ),
          contributeController.whatsappList.isNotEmpty &&
                  !contributeController.isgetloading.value
              ? Row(
                  children: [
                    CustomImageView(
                      imagePath: AssetUrl.addGif,
                      height: 15.h,
                      width: 15.w,
                    ),
                    Padding(
                        padding: EdgeInsets.only(left: 2.w),
                        child: InkWell(
                            onTap: () {
                              Get.to(() => const WhatsAppEditLink());
                            },
                            child: Text("Add Group",
                                style: CustomTextStyles.titleSmallIndigo500)))
                  ],
                )
              : const SizedBox(),
        ]));
  }

  /// Section Widget
  Widget _buildFrame1(BuildContext context) {
    final controller = Get.find<ContributeController>();
    return Obx(() => controller.isgetloading.isTrue
        ? SizedBox(
            height: SizeConfig.screenHeight * .1,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SpinKitDualRing(
                    color: ColorUtil.blueColor,
                    lineWidth: 4.sp,
                    size: 40.0.sp,
                  ),
                  const Text(
                    "loading..",
                    style: TextStyle(
                      color: Colors.white,
                    ),
                  )
                ],
              ),
            ),
          )
        : controller.whatsappList.isEmpty
            ? SizedBox(
                height: 75.h,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text(
                      "You don't have any whatsapp groups",
                      // style: context.dividerTextLarge,
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 4.h),
                    TextButton.icon(
                      icon: Image.asset('assets/images/whatsapp.png',
                          height: 24, width: 24, fit: BoxFit.cover),
                      label: const Text('Add Group'),
                      onPressed: () {
                        Get.to(() => const WhatsAppEditLink());
                      },
                    )
                  ],
                ),
              )
            : controller.whatsappList.isNotEmpty
                ? ListView.separated(
                    shrinkWrap: true,
                    separatorBuilder: (context, index) {
                      return SizedBox(height: 8.h);
                    },
                    itemCount: controller.whatsappList.length,
                    itemBuilder: (context, index) {
                      final whatsapp = controller.whatsappList[index];
                      return WhatsAppWidget(
                        whatsappName: whatsapp.whatsappGroupName ?? "",
                        whatsapp: whatsapp,
                        whatsappProfile: whatsapp.whatsAppProfile ?? "",
                      );
                    },
                  )
                : Text(
                    "You don't have any whatsapp groups yet, click the 'Add Group' button to add.",
                    style: context.dividerTextLarge,
                    textAlign: TextAlign.center,
                  ));
  }

  Widget _buildTabs(BuildContext context) {
    return Column(
      children: [
        Column(
          children: [
            Container(
              padding: EdgeInsets.all(5.h),
              decoration: AppDecoration.fillSlate.copyWith(
                borderRadius: BorderRadiusStyle.roundedBorder6,
              ),
              child: TabBar(
                physics: const ClampingScrollPhysics(),
                //padding: const EdgeInsets.only(left: 5, right: 5),
                unselectedLabelColor: Colors.black,
                labelColor: Theme.of(context).primaryColor,
                indicatorSize: TabBarIndicatorSize.tab,
                dividerColor: Colors.transparent,
                controller: tabController,
                indicator: BoxDecoration(
                    borderRadius: BorderRadius.circular(5),
                    color: Colors.white),
                tabs: [
                  Tab(
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 1.w),
                      child: const Text("Services"),
                    ),
                  ),
                  Tab(
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 1.w),
                      child: const Text("Transactions"),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(
              height: 10.h,
            ),
            Obx(
              () => SizedBox(
                height: extendedScreen.value,
                child: Column(
                  children: [
                    Expanded(
                      child: TabBarView(
                        controller: tabController,
                        children: [
                          const ServicesWidget(),
                          const TransactionWidget(),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            )
          ],
        )
      ],
    );
  }
}

Color getkittyStatusColor(String status) {
  switch (status.toLowerCase()) {
    case "active":
      return const Color(0xFF56AF57);
    case "completed":
      return const Color(0xFF56AF57);
    case "settlement initiated":
      return const Color.fromARGB(255, 206, 104, 192);

    default:
      return const Color(0xFFEE5B60);
  }
}

String getGreeting() {
  var hour = DateTime.now().hour;
  if (hour < 12) {
    return "Good morning";
  } else if (hour < 18) {
    return "Good afternoon";
  } else {
    return "Good evening";
  }
}

String getSettlementType(int type) {
  switch (type) {
    case 0:
      return "Wallet";
    case 1:
      return "TILL";
    case 2:
      return "PAYBILL";
    case 3:
      return "BANK";
    default:
      return "Invalid input";
  }
}

String getNetworkName(String code) {
  switch (code) {
    case "0":
      return "Sasapay";
    case "1":
      return "Onekitty";
    case "63902":
      return "M-Pesa";
    case "63903":
      return "Airtel Money";
    case "63907":
      return "T-kash";
    case "55":
      return "Visa";
    default:
      return "UNKNOWN";
  }
}

// Add this class for the pinned header delegate
class PinnedHeaderDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;
  final double height;

  PinnedHeaderDelegate({required this.child, required this.height});

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return SizedBox.expand(child: child);
  }

  @override
  double get maxExtent => height;

  @override
  double get minExtent => height;

  @override
  bool shouldRebuild(PinnedHeaderDelegate oldDelegate) {
    return oldDelegate.child != child || oldDelegate.height != height;
  }
}

// Update your KittyFlexibleSpacebar class to include the parallax effect
class KittyFlexibleSpacebar extends StatelessWidget {
  const KittyFlexibleSpacebar({super.key});

  @override
  Widget build(BuildContext context) {
    final ContributeController getKittyController =
        Get.find<ContributeController>();
    return LayoutBuilder(
      builder: (context, constraints) {
        final scrollOffset = constraints.biggest.height - constraints.maxHeight;
        return Transform.translate(
          offset: Offset(0, scrollOffset * 0.5),
          child: Obx(
            () => PageView.builder(
              itemCount: getKittyController.kittyMedia.length,
              itemBuilder: (context, index) {
                return FastCachedImage(
                  url: getKittyController.kittyMedia[index].url ?? "",
                  fit: BoxFit.cover,
                  loadingBuilder: (context, downloadData) =>
                      ValueListenableBuilder(
                          valueListenable: downloadData.progressPercentage,
                          builder: (context, value, child) => Center(
                              child: CircularProgressIndicator(value: value))),
                  errorBuilder: (context, url, error) =>
                      const Icon(Icons.error, color: Colors.red),
                );
              },
            ),
          ),
        );
      },
    );
  }
}
