name: onekitty
version: 4.12.0+40
publish_to: none
environment:
  sdk: ">=3.0.0 <4.0.0"
description: onekitty helps to ease in funds collection and contributions betweeen groups
dependencies:
  
  animations: ^2.0.11
  app_links: ^6.1.1
  appinio_video_player_plus: ^1.0.7
  auto_size_text: ^3.0.0
  fast_cached_network_image: ^1.3.3+5
  camera: ^0.11.1 
  carousel_slider: ^5.0.0
  confetti: ^0.8.0
  connectivity_checker: ^1.1.0
  # country_pickers: ^3.0.1
  crypto: ^3.0.3
  csv: any
  currency_formatter: ^2.2.0
  date_time_format: ^2.0.1
  device_info_plus: ^10.1.0
  dio: ^5.4.1
  easy_splash_screen: ^1.0.4
  encrypt: ^5.0.3
  excel: ^4.0.2
  file_picker: ^10.0.0
  firebase_analytics: ^12.0.0
  firebase_auth: ^6.0.1
  firebase_core: ^4.0.0
  firebase_crashlytics: ^5.0.0
  firebase_messaging: ^16.0.0
  firebase_remote_config: ^6.0.0
  firebase_storage: ^13.0.0
  fl_country_code_picker: ^0.1.9+1
  flex_color_scheme: ^8.2.0
  flutter:
    sdk: flutter
  flutter_animate: ^4.5.0
  flutter_animated_dialog_updated: ^1.0.1
  flutter_contacts: ^1.1.7+1
  flutter_datetime_picker_plus: ^2.2.0
  flutter_easyloading: ^3.0.5
  flutter_expandable_fab: ^2.0.0
  # flutter_flip_clock: ^0.0.1
  flutter_inappwebview: ^6.1.5
  flutter_local_notifications: ^19.0.0
  flutter_map: ^8.0.0
  flutter_markdown: ^0.7.4+3
  flutter_pdfview: ^1.3.2
  flutter_quill: ^11.1.2
  flutter_quill_delta_from_html: ^1.5.0

  flutter_screenutil: ^5.9.0
  flutter_spinkit: ^5.2.0
  flutter_svg: ^2.0.9
  flutter_tawkto: ^0.0.5
  fluttertoast: ^8.2.8
  # get: ^4.7.2
  get: ^5.0.0-release-candidate-9.3.2
  get_storage: ^2.1.1
  glassmorphism: ^3.0.0
  google_fonts: ^6.1.0
  google_generative_ai: ^0.4.6
  
  # google_ml_kit: any
  grouped_list: ^6.0.0
  image_picker: ^1.1.2
  insta_image_viewer: ^1.0.4
  intl: any
  intl_phone_number_input: ^0.7.4
  latlong2: ^0.9.1
  liquid_swipe: ^3.1.0
  local_auth: ^2.1.7
  logger: ^2.0.2+1
  lottie: ^3.2.0
  geolocator: ^14.0.2
  mime: ^2.0.0
  mobile_scanner: ^7.0.1
  number_paginator: ^1.0.1
  open_file: any
  otp_autofill: 
   git: 
    https://github.com/vithanichandresh/flutter-otp-autofill
  package_info_plus: ^8.0.0
  path: ^1.9.0
  path_provider: ^2.0.15
  pdf: ^3.10.8
  permission_handler: any
  photo_view: ^0.15.0
  pinput: ^5.0.0
  popover: ^0.3.0
  printing: any
  pull_to_refresh: ^2.0.0
  qr_flutter: ^4.1.0
  # android_intent_plus: ^5.3.0
  readmore: ^3.0.0
  responsive_framework: ^1.0.0
  share_plus: ^11.1.0
  # share_whatsapp: ^1.0.2
  shimmer: ^3.0.0
  skeletonizer: ^2.0.1
  smooth_page_indicator: ^1.1.0
  snow_fall_animation: ^0.0.1+3
  syncfusion_flutter_xlsio: ^29.1.35
  shorebird_code_push: ^2.0.4
  time_since: ^1.0.1
  timeline_tile: ^2.0.0
  upgrader: ^11.2.0
  url_launcher: ^6.1.11
  url_strategy: ^0.3.0
  vsc_quill_delta_to_html: ^1.0.5
  multi_contact_picker: 
    git: 
      https://github.com/gideonsigilai/flutter_multi_contact_picker.git
  geocoding: ^4.0.0
  # basic_app_security: ^1.0.4
  flutter_device_imei: ^0.0.3 
  cached_network_image: ^3.4.1
  video_player: ^2.9.5
  chat_gpt_sdk: ^3.1.5
  # safe_device: ^1.2.1
  # tuple: ^2.0.2
  # webview_flutter: ^4.10.0
dev_dependencies:
  change_app_package_name: ^1.1.0
  flutter_launcher_icons: ^0.14.1
  flutter_lints: ^6.0.0
  flutter_native_splash: ^2.4.1
  flutter_test:
    sdk: flutter
  pubspec_dependency_sorter: ^1.0.4
dependency_overrides:
  device_info_plus: ^11.3.3
  pdf_widget_wrapper: ^1.0.4
  camera_android_camerax: ^0.6.18+3
  # camera_android_camerax: ^0.6.18+3
  # super_native_extensions: ^0.8.24
  super_native_extensions: ^0.9.1
  sqflite_android: 2.4.1
  
flutter_icons:
  image_path: assets/images/launcher_2.png
  android: true
  ios: true
  remove_alpha_ios: true
flutter_native_splash:
  color: "#4355B6"
  image: assets/images/logo8.png
  android_12:
    image: assets/images/launcher.png
    color: "#4355B6"
    icon_background_color: "#4355B6"
  android: true
  ios: true
  web: false
  fullscreen: true
flutter:
  assets:
    - assets/images/
    - assets/lottie_animations/
    - assets/images/icons/
    - assets/
    - assets/resources/
    - shorebird.yaml
  fonts:
    - family: Poppins
      fonts:
        - asset: assets/fonts/PoppinsSemiBold.ttf
          weight: 600
        - asset: assets/fonts/PoppinsRegular.ttf
          weight: 400
        - asset: assets/fonts/PoppinsMedium.ttf
          weight: 500
    - family: Inter
      fonts:
        - asset: assets/fonts/InterRegular.ttf
          weight: 400
    - family: Sen
      fonts:
        - asset: assets/fonts/SenRegular.ttf
          weight: 400
    - family: Rustic
      fonts:
        - asset: assets/fonts/SELINCAH.otf
          weight: 400
  uses-material-design: true
fonts:
  - family: Sen
    fonts:
      - asset: assets/fonts/Sen-VariableFont_wght.ttf
