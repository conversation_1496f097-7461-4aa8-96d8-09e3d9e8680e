# Generated code do not commit.
file(TO_CMAKE_PATH "D:\\sdksss\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "E:\\onekitty Projects\\new\\mobile-onekitty-v2" PROJECT_DIR)

set(FLUTTER_VERSION "4.12.0+40" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 4 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 12 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 40 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=D:\\sdksss\\flutter"
  "PROJECT_DIR=E:\\onekitty Projects\\new\\mobile-onekitty-v2"
  "FLUTTER_ROOT=D:\\sdksss\\flutter"
  "FLUTTER_EPHEMERAL_DIR=E:\\onekitty Projects\\new\\mobile-onekitty-v2\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=E:\\onekitty Projects\\new\\mobile-onekitty-v2"
  "FLUTTER_TARGET=E:\\onekitty Projects\\new\\mobile-onekitty-v2\\lib\\main.dart"
  "DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzUuMw==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049YTQwMmQ5YTQzNw==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049ZGRmNDdkZDNmZg==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My45LjI="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=E:\\onekitty Projects\\new\\mobile-onekitty-v2\\.dart_tool\\package_config.json"
)
